# Complete Signal Logging System - Theoretical Documentation

## Overview
The signal logging system is a comprehensive framework that tracks the entire lifecycle of trading signals from generation to result evaluation. It maintains both in-memory and persistent storage for real-time performance tracking.

## System Architecture

### 1. Data Structures

#### Global Variables
```python
# In-memory storage for active (pending) signals
active_signals: List[Dict[str, Any]] = []

# Performance statistics for each trading pair
performance_summary: Dict[str, Dict[str, int]] = {}

# All signals for the current day (persistent storage)
daily_signals: List[Dict[str, Any]] = []
```

#### Signal Data Structure
```python
signal_data = {
    "pair": "EURGBP_otc",                    # Trading pair
    "timestamp": "14:30:25",                 # Signal generation time
    "date": "2025-07-29",                    # Signal date
    "direction": "call",                     # Trade direction (call/put)
    "price": 1.10250,                       # Entry price
    "strategy_used": "ECHO_SNIPER",          # Strategy name
    "timeframe": "M1",                       # Timeframe used
    "result": "pending",                     # Initial status
    "final_price": None,                     # Will be updated later
    "evaluation_time": None,                 # Will be updated later
    # Strategy-specific fields
    "pattern_used": "GGRRG",                 # Echo Sniper pattern
    "confidence_score": 0.75,                # Signal confidence
    "pattern_win_rate": 68.5                 # Historical win rate
}
```

## Complete Signal Flow Process

### Phase 1: System Initialization

#### 1.1 Logger Initialization
```python
def initialize_signal_logger():
    # Create signals directory if not exists
    signals_dir = "signals"
    os.makedirs(signals_dir, exist_ok=True)
    
    # Load today's signals from file
    today = datetime.now().strftime("%Y-%m-%d")
    signals_file = f"signals/signals_{today}.json"
    
    # Rebuild performance summary from existing signals
    # This ensures continuity across bot restarts
```

**Purpose**: Ensures data persistence and continuity when bot restarts.

### Phase 2: Signal Generation & Logging

#### 2.1 Signal Generation (Model.py)
```python
async def generate_signal(asset, strategy_engine, ...):
    # 1. Fetch market data
    df = await fetch_quotex_market_data(asset, timeframe, fetch_count)
    
    # 2. Apply strategy (Echo Sniper)
    signal, confidence = strategy_engine.evaluate_echo_sniper_strategy(df)
    
    # 3. Apply filters (uptrend/downtrend)
    if best_signal == "put" and uptrend_detected:
        best_signal = "hold"  # Filter out
    
    # 4. Create signal data structure
    signal_data = {
        "pair": asset,
        "timestamp": now.strftime("%H:%M:%S"),
        "direction": best_signal,
        "price": current_price,
        "result": "pending",  # Initial state
        # ... other fields
    }
    
    # 5. Save signal
    save_signal(signal_data)
    
    return signal_data
```

#### 2.2 Signal Saving Process
```python
def save_signal(signal_data):
    global active_signals, daily_signals
    
    # 1. Add to active signals (for pending evaluation)
    if signal_data.get('result') == 'pending':
        active_signals.append(signal_data.copy())
    
    # 2. Add to daily signals (permanent record)
    daily_signals.append(signal_data.copy())
    
    # 3. Save to daily file immediately
    today = datetime.now().strftime("%Y-%m-%d")
    signals_file = f"signals/signals_{today}.json"
    
    with open(signals_file, 'w') as f:
        json.dump(daily_signals, f, indent=2, default=str)
```

**Key Points**:
- **Dual Storage**: Both memory (active_signals) and disk (daily_signals)
- **Immediate Persistence**: Every signal is saved to file immediately
- **Pending State**: New signals start with "pending" result

### Phase 3: Signal Evaluation & Result Determination

#### 3.1 Evaluation Trigger (Model.py - Main Loop)
```python
# Every minute, before generating new signals:
async def evaluate_pair_signal(asset):
    # 1. Fetch current price (minimal data - 3 candles)
    eval_df = await fetch_quotex_market_data(asset, granularity, 3)
    current_price = eval_df['close'].iloc[-1]
    
    # 2. Evaluate pending signal
    result = evaluate_last_signal(asset, current_price)
    
    return result['result'] if result else None

# Evaluate ALL pairs concurrently
evaluation_tasks = [evaluate_pair_signal(asset) for asset in selected_assets]
evaluation_results = await asyncio.gather(*evaluation_tasks)
```

#### 3.2 Signal Evaluation Logic
```python
def evaluate_last_signal(pair: str, new_price: float):
    global active_signals, performance_summary, daily_signals
    
    # 1. Find pending signal for this pair
    pending_signal = None
    for i, signal in enumerate(active_signals):
        if signal['pair'] == pair and signal.get('result') == 'pending':
            pending_signal = signal
            signal_index = i
            break
    
    if not pending_signal:
        return None  # No pending signal
    
    # 2. Determine win/loss based on direction
    signal_price = pending_signal['price']
    direction = pending_signal['direction']
    
    if direction == "call":
        result = "win" if new_price > signal_price else "loss"
    else:  # direction == "put"
        result = "win" if new_price < signal_price else "loss"
    
    # 3. Update signal with result
    evaluation_time = datetime.now().strftime("%H:%M:%S")
    pending_signal.update({
        'result': result,
        'final_price': new_price,
        'evaluation_time': evaluation_time
    })
    
    # 4. Remove from active signals (no longer pending)
    active_signals.pop(signal_index)
    
    # 5. Update daily signals file
    for daily_signal in daily_signals:
        if (daily_signal['pair'] == pair and 
            daily_signal['timestamp'] == pending_signal['timestamp']):
            daily_signal.update({
                'result': result,
                'final_price': new_price,
                'evaluation_time': evaluation_time
            })
            break
    
    # 6. Save updated daily signals to file
    today = datetime.now().strftime("%Y-%m-%d")
    signals_file = f"signals/signals_{today}.json"
    with open(signals_file, 'w') as f:
        json.dump(daily_signals, f, indent=2, default=str)
    
    # 7. Update performance summary
    update_summary(pair, result)
    
    return {
        'pair': pair,
        'result': result,
        'signal_price': signal_price,
        'final_price': new_price,
        'direction': direction
    }
```

**Result Determination Logic**:
- **CALL Signal**: WIN if new_price > signal_price, LOSS otherwise
- **PUT Signal**: WIN if new_price < signal_price, LOSS otherwise

### Phase 4: Performance Summary Updates

#### 4.1 Summary Update Process
```python
def update_summary(pair: str, result: str):
    global performance_summary
    
    # Initialize pair if not exists
    if pair not in performance_summary:
        performance_summary[pair] = {'total': 0, 'wins': 0, 'losses': 0}
    
    # Update counters
    performance_summary[pair]['total'] += 1
    if result == 'win':
        performance_summary[pair]['wins'] += 1
    else:
        performance_summary[pair]['losses'] += 1
```

#### 4.2 Summary Display
```python
def print_summary_box(selected_pairs):
    # Display table header
    print("Pairs           | Total Signals | Wins | Losses")
    print("-" * 48)
    
    # Display each pair's performance
    for pair in selected_pairs:
        if pair in performance_summary:
            stats = performance_summary[pair]
            total = stats['total']
            wins = stats['wins']
            losses = stats['losses']
        else:
            total = wins = losses = 0
        
        print(f"{pair:<15} |      {total:02d}       |  {wins:02d}  |   {losses:02d}")
    
    # Overall summary
    print("-" * 48)
    print(f"{'Overall':<15} |      {total_signals:02d}       |  {total_wins:02d}  |   {total_losses:02d}")
```

## File Storage System

### Daily File Structure
```
signals/
├── signals_2025-07-29.json
├── signals_2025-07-28.json
└── signals_2025-07-27.json
```

### File Content Example
```json
[
  {
    "pair": "EURGBP_otc",
    "timestamp": "14:30:25",
    "date": "2025-07-29",
    "direction": "call",
    "price": 1.1025,
    "strategy_used": "ECHO_SNIPER",
    "timeframe": "M1",
    "result": "win",
    "final_price": 1.1028,
    "evaluation_time": "14:31:25",
    "pattern_used": "GGRRG",
    "confidence_score": 0.75
  }
]
```

## Key Features

### 1. Real-time Processing
- Signals are evaluated every minute
- Results are determined immediately when new price data arrives
- Performance summary updates in real-time

### 2. Data Persistence
- All signals are saved to daily JSON files
- System can recover from restarts
- Historical data is preserved

### 3. Multi-pair Support
- Each pair is tracked independently
- Concurrent evaluation for all pairs
- Individual performance statistics

### 4. Error Handling
- Graceful handling of missing data
- Silent failures for speed optimization
- Data integrity checks

## Implementation for New Bot

### Required Components
1. **Signal Data Structure**: Define comprehensive signal format
2. **Storage System**: Implement dual storage (memory + file)
3. **Evaluation Logic**: Create win/loss determination rules
4. **Summary System**: Build performance tracking
5. **File Management**: Handle daily file rotation

### Critical Considerations
1. **Timing**: Ensure evaluation happens after signal expiry
2. **Concurrency**: Handle multiple pairs simultaneously
3. **Persistence**: Save data immediately to prevent loss
4. **Recovery**: Load existing data on startup
5. **Performance**: Optimize for real-time processing

This system provides complete signal lifecycle management with robust data persistence and real-time performance tracking.

## Detailed Implementation Steps for New Bot

### Step 1: Initialize Signal Logger
```python
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any

# Global storage
active_signals: List[Dict[str, Any]] = []
performance_summary: Dict[str, Dict[str, int]] = {}
daily_signals: List[Dict[str, Any]] = []

def initialize_signal_logger():
    global daily_signals, performance_summary

    # Create signals directory
    signals_dir = "signals"
    os.makedirs(signals_dir, exist_ok=True)

    # Load today's signals
    today = datetime.now().strftime("%Y-%m-%d")
    signals_file = os.path.join(signals_dir, f"signals_{today}.json")

    if os.path.exists(signals_file):
        with open(signals_file, 'r') as f:
            daily_signals = json.load(f)

        # Rebuild performance summary
        performance_summary = {}
        for signal in daily_signals:
            if signal.get('result') in ['win', 'loss']:
                pair = signal['pair']
                if pair not in performance_summary:
                    performance_summary[pair] = {'total': 0, 'wins': 0, 'losses': 0}

                performance_summary[pair]['total'] += 1
                if signal['result'] == 'win':
                    performance_summary[pair]['wins'] += 1
                else:
                    performance_summary[pair]['losses'] += 1
    else:
        daily_signals = []
        with open(signals_file, 'w') as f:
            json.dump([], f)
```

### Step 2: Signal Creation and Saving
```python
def create_signal_data(pair: str, direction: str, price: float, strategy: str, **kwargs):
    """Create standardized signal data structure"""
    now = datetime.now()

    return {
        "pair": pair,
        "timestamp": now.strftime("%H:%M:%S"),
        "date": now.strftime("%Y-%m-%d"),
        "direction": direction,  # 'call' or 'put'
        "price": price,
        "strategy_used": strategy,
        "timeframe": kwargs.get('timeframe', 'M1'),
        "result": "pending",
        "final_price": None,
        "evaluation_time": None,
        "confidence_score": kwargs.get('confidence', 0.0),
        # Add any strategy-specific fields
        **{k: v for k, v in kwargs.items() if k not in ['timeframe', 'confidence']}
    }

def save_signal(signal_data: Dict[str, Any]) -> bool:
    """Save signal to both memory and file"""
    global active_signals, daily_signals

    try:
        # Add to active signals for evaluation
        if signal_data.get('result') == 'pending':
            active_signals.append(signal_data.copy())

        # Add to daily signals for persistence
        daily_signals.append(signal_data.copy())

        # Save to file immediately
        today = datetime.now().strftime("%Y-%m-%d")
        signals_file = f"signals/signals_{today}.json"

        with open(signals_file, 'w') as f:
            json.dump(daily_signals, f, indent=2, default=str)

        return True
    except Exception as e:
        print(f"Error saving signal: {e}")
        return False
```

### Step 3: Signal Evaluation System
```python
def evaluate_last_signal(pair: str, new_price: float) -> Optional[Dict[str, Any]]:
    """Evaluate pending signal and determine win/loss"""
    global active_signals, performance_summary, daily_signals

    # Find pending signal for this pair
    pending_signal = None
    signal_index = None

    for i, signal in enumerate(active_signals):
        if signal['pair'] == pair and signal.get('result') == 'pending':
            pending_signal = signal
            signal_index = i
            break

    if not pending_signal:
        return None

    # Determine result based on direction
    signal_price = pending_signal['price']
    direction = pending_signal['direction']

    if direction == "call":
        result = "win" if new_price > signal_price else "loss"
    else:  # direction == "put"
        result = "win" if new_price < signal_price else "loss"

    # Update signal with result
    evaluation_time = datetime.now().strftime("%H:%M:%S")
    pending_signal.update({
        'result': result,
        'final_price': new_price,
        'evaluation_time': evaluation_time
    })

    # Remove from active signals
    active_signals.pop(signal_index)

    # Update daily signals
    for daily_signal in daily_signals:
        if (daily_signal['pair'] == pair and
            daily_signal['timestamp'] == pending_signal['timestamp'] and
            daily_signal.get('result') == 'pending'):
            daily_signal.update({
                'result': result,
                'final_price': new_price,
                'evaluation_time': evaluation_time
            })
            break

    # Save updated signals to file
    today = datetime.now().strftime("%Y-%m-%d")
    signals_file = f"signals/signals_{today}.json"
    with open(signals_file, 'w') as f:
        json.dump(daily_signals, f, indent=2, default=str)

    # Update performance summary
    update_summary(pair, result)

    return {
        'pair': pair,
        'result': result,
        'signal_price': signal_price,
        'final_price': new_price,
        'direction': direction,
        'evaluation_time': evaluation_time
    }

def update_summary(pair: str, result: str):
    """Update performance summary for a pair"""
    global performance_summary

    if pair not in performance_summary:
        performance_summary[pair] = {'total': 0, 'wins': 0, 'losses': 0}

    performance_summary[pair]['total'] += 1
    if result == 'win':
        performance_summary[pair]['wins'] += 1
    else:
        performance_summary[pair]['losses'] += 1
```

### Step 4: Main Bot Integration
```python
async def main_trading_loop():
    """Main bot loop with signal logging integration"""

    # Initialize signal logger
    initialize_signal_logger()

    while True:
        # Step 1: Evaluate existing signals
        for pair in selected_pairs:
            current_price = await get_current_price(pair)
            result = evaluate_last_signal(pair, current_price)

            if result:
                print(f"Signal result: {pair} -> {result['result'].upper()}")

        # Step 2: Generate new signals
        for pair in selected_pairs:
            signal, confidence, price = await generate_signal(pair)

            if signal in ['call', 'put']:
                # Create signal data
                signal_data = create_signal_data(
                    pair=pair,
                    direction=signal,
                    price=price,
                    strategy="YOUR_STRATEGY",
                    confidence=confidence
                )

                # Save signal
                save_signal(signal_data)
                print(f"Signal generated: {pair} -> {signal.upper()}")

        # Step 3: Display summary
        display_performance_summary(selected_pairs)

        # Wait for next cycle
        await asyncio.sleep(60)  # 1 minute cycle

def display_performance_summary(selected_pairs: List[str]):
    """Display performance summary table"""
    print("\nPairs           | Total Signals | Wins | Losses")
    print("-" * 48)

    total_signals = total_wins = total_losses = 0

    for pair in selected_pairs:
        if pair in performance_summary:
            stats = performance_summary[pair]
            total = stats['total']
            wins = stats['wins']
            losses = stats['losses']
        else:
            total = wins = losses = 0

        pair_display = pair[:15] if len(pair) <= 15 else pair[:12] + "..."
        print(f"{pair_display:<15} |      {total:02d}       |  {wins:02d}  |   {losses:02d}")

        total_signals += total
        total_wins += wins
        total_losses += losses

    print("-" * 48)
    print(f"{'Overall':<15} |      {total_signals:02d}       |  {total_wins:02d}  |   {total_losses:02d}")
    print("=" * 60)
```

## Critical Implementation Notes

### 1. Timing Considerations
- **Signal Generation**: Happens at specific intervals (e.g., every minute)
- **Signal Evaluation**: Must happen AFTER the signal's intended duration
- **File Saving**: Should be immediate to prevent data loss

### 2. Error Handling
```python
try:
    result = evaluate_last_signal(pair, current_price)
except Exception as e:
    print(f"Error evaluating signal for {pair}: {e}")
    # Continue with other pairs
```

### 3. Data Integrity
- Always use `.copy()` when storing signals to prevent reference issues
- Validate signal data before saving
- Handle file corruption gracefully

### 4. Performance Optimization
- Use concurrent evaluation for multiple pairs
- Minimize file I/O operations
- Cache frequently accessed data

This comprehensive system ensures reliable signal tracking with complete data persistence and real-time performance monitoring.
